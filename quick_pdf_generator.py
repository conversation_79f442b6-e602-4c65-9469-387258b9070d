#!/usr/bin/env python3
"""
Generatore PDF veloce e ottimizzato per il sito promptingguide.ai
Versione semplificata che processa solo le pagine principali.
"""

import asyncio
import json
import re
import time
from pathlib import Path
from typing import Dict, List

from bs4 import BeautifulSoup, NavigableString
from playwright.async_api import async_playwright
from weasyprint import HTML


class QuickPDFGenerator:
    def __init__(self, base_url: str = "https://www.promptingguide.ai/it"):
        self.base_url = base_url
        self.output_dir = Path("output_quick")
        self.output_dir.mkdir(exist_ok=True)
        self.pages = []

    async def get_main_pages(self):
        """Estrae solo le pagine principali per un PDF più gestibile."""
        main_pages = [
            {"url": f"{self.base_url}", "title": "Introduzione al Prompt Engineering"},
            {"url": f"{self.base_url}/introduction", "title": "Introduzione"},
            {"url": f"{self.base_url}/introduction/basics", "title": "Fondamentali del Prompt"},
            {"url": f"{self.base_url}/introduction/elements", "title": "Elementi di un Prompt"},
            {"url": f"{self.base_url}/introduction/tips", "title": "Suggerimenti per la Progettazione"},
            {"url": f"{self.base_url}/introduction/examples", "title": "Esempi di Prompt"},
            {"url": f"{self.base_url}/techniques", "title": "Tecniche"},
            {"url": f"{self.base_url}/techniques/zeroshot", "title": "Prompt Zero-shot"},
            {"url": f"{self.base_url}/techniques/fewshot", "title": "Prompt Few-shot"},
            {"url": f"{self.base_url}/techniques/cot", "title": "Chain-of-Thought"},
            {"url": f"{self.base_url}/techniques/consistency", "title": "Self-Consistency"},
            {"url": f"{self.base_url}/applications", "title": "Applicazioni"},
            {"url": f"{self.base_url}/applications/generating", "title": "Generazione di Dati"},
            {"url": f"{self.base_url}/applications/coding", "title": "Generazione di Codice"},
            {"url": f"{self.base_url}/models", "title": "Modelli"},
            {"url": f"{self.base_url}/models/chatgpt", "title": "ChatGPT"},
            {"url": f"{self.base_url}/models/gpt-4", "title": "GPT-4"},
            {"url": f"{self.base_url}/risks", "title": "Rischi e Abusi"},
            {"url": f"{self.base_url}/risks/adversarial", "title": "Prompt Conflittuale"},
            {"url": f"{self.base_url}/risks/factuality", "title": "Fattualità"},
        ]
        
        print(f"📋 Processando {len(main_pages)} pagine principali...")
        return main_pages

    async def scrape_page(self, url: str, title: str) -> Dict:
        """Scraping ottimizzato di una singola pagina."""
        print(f"📄 {title}")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                await page.goto(url, wait_until="networkidle", timeout=30000)
                await page.wait_for_timeout(1000)
                
                # Estrae solo il contenuto essenziale
                content = await page.evaluate("""
                    () => {
                        // Rimuove elementi non necessari
                        const toRemove = [
                            'nav', 'header', 'footer', 'aside', 'button',
                            '.nextra-breadcrumb', '.nextra-cards', '.nextra-callout',
                            '[class*="nx-"]', 'script', 'style', 'svg'
                        ];
                        
                        toRemove.forEach(selector => {
                            try {
                                document.querySelectorAll(selector).forEach(el => el.remove());
                            } catch(e) {}
                        });
                        
                        // Trova il contenuto principale
                        const main = document.querySelector('main') || 
                                    document.querySelector('article') ||
                                    document.querySelector('.content') ||
                                    document.body;
                        
                        if (main) {
                            // Pulisce attributi
                            main.querySelectorAll('*').forEach(el => {
                                ['class', 'style', 'data-language', 'data-theme'].forEach(attr => {
                                    el.removeAttribute(attr);
                                });
                            });
                            
                            return main.innerHTML;
                        }
                        return '';
                    }
                """)
                
                return {
                    'title': title,
                    'html': content,
                    'success': True
                }
                
            except Exception as e:
                print(f"❌ Errore: {e}")
                return {'title': title, 'html': '', 'success': False}
            finally:
                await browser.close()

    def clean_html(self, html: str) -> str:
        """Pulizia finale dell'HTML."""
        if not html:
            return ""
            
        soup = BeautifulSoup(html, 'html.parser')
        
        # Rimuove elementi problematici
        for element in soup(['script', 'style', 'svg', 'button']):
            element.decompose()
        
        # Pulisce il testo
        text = soup.get_text()
        if len(text.strip()) < 50:  # Pagina troppo corta, probabilmente vuota
            return ""
        
        return str(soup)

    def generate_pdf_html(self) -> str:
        """Genera HTML ottimizzato per PDF."""
        html = f"""<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <title>Guida al Prompt Engineering</title>
    <style>
        @page {{
            size: A4;
            margin: 2cm;
            @bottom-center {{
                content: "Pagina " counter(page);
                font-size: 9pt;
                color: #666;
            }}
        }}
        
        body {{
            font-family: 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            font-size: 11pt;
        }}
        
        .title-page {{
            text-align: center;
            padding: 5cm 0;
            page-break-after: always;
        }}
        
        .title-page h1 {{
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 0.5em;
        }}
        
        .chapter {{
            page-break-before: always;
            margin-bottom: 2em;
        }}
        
        .chapter-title {{
            color: #2c3e50;
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 1em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5em;
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
            page-break-after: avoid;
        }}
        
        h1 {{ font-size: 1.4em; }}
        h2 {{ font-size: 1.2em; }}
        h3 {{ font-size: 1.1em; }}
        
        p {{
            margin: 0.8em 0;
            text-align: justify;
        }}
        
        pre, code {{
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }}
        
        pre {{
            padding: 1em;
            margin: 1em 0;
            page-break-inside: avoid;
        }}
        
        code {{
            padding: 0.2em 0.4em;
        }}
        
        blockquote {{
            border-left: 4px solid #3498db;
            margin: 1em 0;
            padding: 0.5em 1em;
            background-color: #f8f9fa;
        }}
        
        ul, ol {{
            margin: 0.8em 0;
            padding-left: 2em;
        }}
        
        li {{
            margin: 0.3em 0;
        }}
        
        strong {{
            font-weight: 600;
            color: #2c3e50;
        }}
        
        .toc {{
            page-break-after: always;
        }}
        
        .toc h2 {{
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5em;
        }}
        
        .toc ul {{
            list-style: none;
            padding: 0;
        }}
        
        .toc li {{
            margin: 0.5em 0;
            padding: 0.3em 0;
        }}
    </style>
</head>
<body>
    <div class="title-page">
        <h1>Guida al Prompt Engineering</h1>
        <p style="font-size: 1.2em; color: #7f8c8d;">Versione PDF Ottimizzata</p>
        <p style="color: #95a5a6;">Fonte: promptingguide.ai<br>Generato: {time.strftime("%d/%m/%Y")}</p>
    </div>
    
    <div class="toc">
        <h2>Indice</h2>
        <ul>
"""
        
        # Aggiungi indice
        for page in self.pages:
            if page['success'] and page['html'].strip():
                html += f"            <li>{page['title']}</li>\n"
        
        html += """        </ul>
    </div>
"""
        
        # Aggiungi contenuto
        for page in self.pages:
            if page['success'] and page['html'].strip():
                html += f"""
    <div class="chapter">
        <div class="chapter-title">{page['title']}</div>
        <div class="content">{page['html']}</div>
    </div>
"""
        
        html += """
</body>
</html>"""
        
        return html

    async def generate_pdf(self):
        """Processo completo di generazione PDF."""
        print("🚀 Avvio generazione PDF veloce...")
        start_time = time.time()
        
        # 1. Ottieni pagine principali
        main_pages = await self.get_main_pages()
        
        # 2. Scraping delle pagine
        print("📥 Scraping pagine...")
        for page_info in main_pages:
            result = await self.scrape_page(page_info['url'], page_info['title'])
            if result['success']:
                result['html'] = self.clean_html(result['html'])
                if result['html']:  # Solo se ha contenuto
                    self.pages.append(result)
            
            await asyncio.sleep(0.5)  # Pausa tra richieste
        
        print(f"✅ Raccolte {len(self.pages)} pagine con contenuto")
        
        # 3. Genera HTML
        print("📝 Generazione HTML...")
        html_content = self.generate_pdf_html()
        
        # Salva HTML per debug
        html_file = self.output_dir / "quick_guide.html"
        with open(html_file, "w", encoding="utf-8") as f:
            f.write(html_content)
        
        # 4. Genera PDF
        print("📄 Generazione PDF...")
        pdf_file = self.output_dir / "prompt_engineering_guide_quick.pdf"
        
        try:
            HTML(string=html_content, base_url=self.base_url).write_pdf(
                pdf_file,
                optimize_images=True
            )
            
            elapsed_time = time.time() - start_time
            print(f"🎉 PDF generato con successo in {elapsed_time:.2f} secondi!")
            print(f"📄 File: {pdf_file}")
            print(f"📊 Dimensione: {pdf_file.stat().st_size / 1024 / 1024:.2f} MB")
            
        except Exception as e:
            print(f"❌ Errore nella generazione PDF: {e}")


async def main():
    generator = QuickPDFGenerator()
    await generator.generate_pdf()


if __name__ == "__main__":
    asyncio.run(main())
