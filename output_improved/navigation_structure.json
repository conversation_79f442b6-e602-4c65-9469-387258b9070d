[{"href": "/it", "text": "Prompt Engineering Guide", "level": 0, "full_url": "https://www.promptingguide.ai/it"}, {"href": "/it/about", "text": "InformazioniInformazioni", "level": 0, "full_url": "https://www.promptingguide.ai/it/about"}, {"href": "/it/introduction", "text": "Introduzione", "level": 0, "full_url": "https://www.promptingguide.ai/it/introduction"}, {"href": "/it/introduction/settings", "text": "Impostazioni LLM", "level": 1, "full_url": "https://www.promptingguide.ai/it/introduction/settings"}, {"href": "/it/introduction/basics", "text": "I fondamentali del Prompt", "level": 1, "full_url": "https://www.promptingguide.ai/it/introduction/basics"}, {"href": "/it/introduction/elements", "text": "Elementi di un Prompt", "level": 1, "full_url": "https://www.promptingguide.ai/it/introduction/elements"}, {"href": "/it/introduction/tips", "text": "Suggerimenti generali per la progettazione di Prompt", "level": 1, "full_url": "https://www.promptingguide.ai/it/introduction/tips"}, {"href": "/it/introduction/examples", "text": "Esempi di Prompt", "level": 1, "full_url": "https://www.promptingguide.ai/it/introduction/examples"}, {"href": "/it/techniques", "text": "Tecniche", "level": 0, "full_url": "https://www.promptingguide.ai/it/techniques"}, {"href": "/it/techniques/zeroshot", "text": "Prompt Zero-shot", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/zeroshot"}, {"href": "/it/techniques/fewshot", "text": "Prompt Few-shot", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/fewshot"}, {"href": "/it/techniques/cot", "text": "Prompt Chain-of-Thought", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/cot"}, {"href": "/it/techniques/consistency", "text": "Self-Consistency", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/consistency"}, {"href": "/it/techniques/knowledge", "text": "Prompt Generate Knowledge", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/knowledge"}, {"href": "/it/techniques/prompt_chaining", "text": "Prompt Chaining", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/prompt_chaining"}, {"href": "/it/techniques/tot", "text": "Tree of Thoughts", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/tot"}, {"href": "/it/techniques/rag", "text": "Retrieval Augmented Generation", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/rag"}, {"href": "/it/techniques/art", "text": "Automatic Reasoning and Tool-use", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/art"}, {"href": "/it/techniques/ape", "text": "Automatic Prompt Engineer", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/ape"}, {"href": "/it/techniques/activeprompt", "text": "Prompt Attivo", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/activeprompt"}, {"href": "/it/techniques/dsp", "text": "Prompt Directional Stimulus", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/dsp"}, {"href": "/it/techniques/pal", "text": "Program-Aided Language Models", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/pal"}, {"href": "/it/techniques/react", "text": "ReAct", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/react"}, {"href": "/it/techniques/reflexion", "text": "Reflexion", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/reflexion"}, {"href": "/it/techniques/multimodalcot", "text": "Multimodal CoT", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/multimodalcot"}, {"href": "/it/techniques/graph", "text": "Graph Prompt", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/graph"}, {"href": "/it/techniques/meta-prompting", "text": "meta-prompting", "level": 1, "full_url": "https://www.promptingguide.ai/it/techniques/meta-prompting"}, {"href": "/it/applications", "text": "Applicazioni", "level": 0, "full_url": "https://www.promptingguide.ai/it/applications"}, {"href": "/it/applications/generating", "text": "Generazione di dati", "level": 1, "full_url": "https://www.promptingguide.ai/it/applications/generating"}, {"href": "/it/applications/coding", "text": "Generating Code", "level": 1, "full_url": "https://www.promptingguide.ai/it/applications/coding"}, {"href": "/it/applications/workplace_casestudy", "text": "Studio sul caso della classificazione del lavoro dei laureati", "level": 1, "full_url": "https://www.promptingguide.ai/it/applications/workplace_casestudy"}, {"href": "/it/applications/pf", "text": "Prompt Function", "level": 1, "full_url": "https://www.promptingguide.ai/it/applications/pf"}, {"href": "/it/applications/function_calling", "text": "Function Calling", "level": 1, "full_url": "https://www.promptingguide.ai/it/applications/function_calling"}, {"href": "/it/applications/context-caching", "text": "context-caching", "level": 1, "full_url": "https://www.promptingguide.ai/it/applications/context-caching"}, {"href": "/it/applications/finetuning-gpt4o", "text": "finetuning-gpt4o", "level": 1, "full_url": "https://www.promptingguide.ai/it/applications/finetuning-gpt4o"}, {"href": "/it/applications/generating_textbooks", "text": "generating_textbooks", "level": 1, "full_url": "https://www.promptingguide.ai/it/applications/generating_textbooks"}, {"href": "/it/applications/synthetic_rag", "text": "synthetic_rag", "level": 1, "full_url": "https://www.promptingguide.ai/it/applications/synthetic_rag"}, {"href": "/it/prompts", "text": "Prompt Hub", "level": 0, "full_url": "https://www.promptingguide.ai/it/prompts"}, {"href": "/it/prompts/classification", "text": "Classification", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/classification"}, {"href": "/it/prompts/classification/sentiment", "text": "Sentiment Classification", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/classification/sentiment"}, {"href": "/it/prompts/classification/sentiment-fewshot", "text": "Few-Shot Sentiment Classification", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/classification/sentiment-fewshot"}, {"href": "/it/prompts/coding", "text": "Coding", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/coding"}, {"href": "/it/prompts/coding/code-snippet", "text": "Generate Code Snippet", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/coding/code-snippet"}, {"href": "/it/prompts/coding/mysql-query", "text": "Generate MySQL Query", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/coding/mysql-query"}, {"href": "/it/prompts/coding/tikz", "text": "Draw TiKZ Diagram", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/coding/tikz"}, {"href": "/it/prompts/creativity", "text": "Creativity", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/creativity"}, {"href": "/it/prompts/creativity/rhymes", "text": "Rhymes", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/creativity/rhymes"}, {"href": "/it/prompts/creativity/infinite-primes", "text": "Infinite Primes", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/creativity/infinite-primes"}, {"href": "/it/prompts/creativity/interdisciplinary", "text": "Interdisciplinary", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/creativity/interdisciplinary"}, {"href": "/it/prompts/creativity/new-words", "text": "Inventing New Words", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/creativity/new-words"}, {"href": "/it/prompts/evaluation", "text": "Evaluation", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/evaluation"}, {"href": "/it/prompts/evaluation/plato-dialogue", "text": "Evaluate <PERSON>'s Dialogue", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/evaluation/plato-dialogue"}, {"href": "/it/prompts/information-extraction", "text": "Information Extraction", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/information-extraction"}, {"href": "/it/prompts/information-extraction/extract-models", "text": "Extract Model Names", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/information-extraction/extract-models"}, {"href": "/it/prompts/image-generation", "text": "Image Generation", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/image-generation"}, {"href": "/it/prompts/image-generation/alphabet-person", "text": "Draw a Person Using Alphabet", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/image-generation/alphabet-person"}, {"href": "/it/prompts/mathematics", "text": "Mathematics", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/mathematics"}, {"href": "/it/prompts/mathematics/composite-functions", "text": "Evaluating Composite Functions", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/mathematics/composite-functions"}, {"href": "/it/prompts/mathematics/odd-numbers", "text": "Adding Odd Numbers", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/mathematics/odd-numbers"}, {"href": "/it/prompts/question-answering", "text": "Question Answering", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/question-answering"}, {"href": "/it/prompts/question-answering/closed-domain", "text": "Closed Domain Question Answering", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/question-answering/closed-domain"}, {"href": "/it/prompts/question-answering/open-domain", "text": "Open Domain Question Answering", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/question-answering/open-domain"}, {"href": "/it/prompts/question-answering/science-qa", "text": "Science Question Answering", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/question-answering/science-qa"}, {"href": "/it/prompts/reasoning", "text": "Reasoning", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/reasoning"}, {"href": "/it/prompts/reasoning/indirect-reasoning", "text": "Indirect Reasoning", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/reasoning/indirect-reasoning"}, {"href": "/it/prompts/reasoning/physical-reasoning", "text": "Physical Reasoning", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/reasoning/physical-reasoning"}, {"href": "/it/prompts/text-summarization", "text": "Text Summarization", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/text-summarization"}, {"href": "/it/prompts/text-summarization/explain-concept", "text": "Explain A Concept", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/text-summarization/explain-concept"}, {"href": "/it/prompts/truthfulness", "text": "Truthfulness", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/truthfulness"}, {"href": "/it/prompts/truthfulness/identify-hallucination", "text": "Hallucination Identification", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/truthfulness/identify-hallucination"}, {"href": "/it/prompts/adversarial-prompting", "text": "Adversarial Prompting", "level": 1, "full_url": "https://www.promptingguide.ai/it/prompts/adversarial-prompting"}, {"href": "/it/prompts/adversarial-prompting/prompt-injection", "text": "Prompt Injection", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/adversarial-prompting/prompt-injection"}, {"href": "/it/prompts/adversarial-prompting/prompt-leaking", "text": "Prompt Leaking", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/adversarial-prompting/prompt-leaking"}, {"href": "/it/prompts/adversarial-prompting/jailbreaking-llms", "text": "Jailbreaking", "level": 2, "full_url": "https://www.promptingguide.ai/it/prompts/adversarial-prompting/jailbreaking-llms"}, {"href": "/it/models", "text": "<PERSON><PERSON>", "level": 0, "full_url": "https://www.promptingguide.ai/it/models"}, {"href": "/it/models/flan", "text": "<PERSON>lan", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/flan"}, {"href": "/it/models/chatgpt", "text": "ChatGPT", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/chatgpt"}, {"href": "/it/models/llama", "text": "LLaMA", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/llama"}, {"href": "/it/models/gpt-4", "text": "GPT-4", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/gpt-4"}, {"href": "/it/models/mistral-7b", "text": "Mistral 7B", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/mistral-7b"}, {"href": "/it/models/gemini", "text": "Gemini", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/gemini"}, {"href": "/it/models/gemini-advanced", "text": "Gemini Advanced", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/gemini-advanced"}, {"href": "/it/models/gemini-pro", "text": "Gemini 1.5 Pro", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/gemini-pro"}, {"href": "/it/models/phi-2", "text": "Phi-2", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/phi-2"}, {"href": "/it/models/mixtral", "text": "Mixtral", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/mixtral"}, {"href": "/it/models/code-llama", "text": "Code Llama", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/code-llama"}, {"href": "/it/models/olmo", "text": "OLMo", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/olmo"}, {"href": "/it/models/sora", "text": "<PERSON>ra", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/sora"}, {"href": "/it/models/collection", "text": "Collezione di Modelli", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/collection"}, {"href": "/it/models/claude-3", "text": "claude-3", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/claude-3"}, {"href": "/it/models/gemma", "text": "gemma", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/gemma"}, {"href": "/it/models/grok-1", "text": "grok-1", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/grok-1"}, {"href": "/it/models/llama-3", "text": "llama-3", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/llama-3"}, {"href": "/it/models/mistral-large", "text": "mistral-large", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/mistral-large"}, {"href": "/it/models/mixtral-8x22b", "text": "mixtral-8x22b", "level": 1, "full_url": "https://www.promptingguide.ai/it/models/mixtral-8x22b"}, {"href": "/it/risks", "text": "Rischi & Abusi", "level": 0, "full_url": "https://www.promptingguide.ai/it/risks"}, {"href": "/it/risks/adversarial", "text": "Prompt Conflittuale", "level": 1, "full_url": "https://www.promptingguide.ai/it/risks/adversarial"}, {"href": "/it/risks/factuality", "text": "Fattualità", "level": 1, "full_url": "https://www.promptingguide.ai/it/risks/factuality"}, {"href": "/it/risks/biases", "text": "<PERSON><PERSON><PERSON><PERSON>", "level": 1, "full_url": "https://www.promptingguide.ai/it/risks/biases"}, {"href": "/it/papers", "text": "Articoli scientifici", "level": 0, "full_url": "https://www.promptingguide.ai/it/papers"}, {"href": "/it/research", "text": "LLM Research Findings", "level": 0, "full_url": "https://www.promptingguide.ai/it/research"}, {"href": "/it/research/llm-agents", "text": "LLM Agents", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/llm-agents"}, {"href": "/it/research/rag", "text": "RAG for LLMs", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/rag"}, {"href": "/it/research/trustworthiness-in-llms", "text": "Trustworthiness in LLMs", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/trustworthiness-in-llms"}, {"href": "/it/research/groq", "text": "groq", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/groq"}, {"href": "/it/research/guided-cot", "text": "guided-cot", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/guided-cot"}, {"href": "/it/research/infini-attention", "text": "infini-attention", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/infini-attention"}, {"href": "/it/research/llm-reasoning", "text": "llm-reasoning", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/llm-reasoning"}, {"href": "/it/research/llm-recall", "text": "llm-recall", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/llm-recall"}, {"href": "/it/research/llm-tokenization", "text": "llm-tokenization", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/llm-tokenization"}, {"href": "/it/research/rag-faithfulness", "text": "rag-faithfulness", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/rag-faithfulness"}, {"href": "/it/research/rag_hallucinations", "text": "rag_hallucinations", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/rag_hallucinations"}, {"href": "/it/research/synthetic_data", "text": "synthetic_data", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/synthetic_data"}, {"href": "/it/research/thoughtsculpt", "text": "thoughtsculpt", "level": 1, "full_url": "https://www.promptingguide.ai/it/research/thoughtsculpt"}, {"href": "/it/tools", "text": "Strumenti", "level": 0, "full_url": "https://www.promptingguide.ai/it/tools"}, {"href": "/it/notebooks", "text": "Notebook", "level": 0, "full_url": "https://www.promptingguide.ai/it/notebooks"}, {"href": "/it/datasets", "text": "Dataset", "level": 0, "full_url": "https://www.promptingguide.ai/it/datasets"}, {"href": "/it/readings", "text": "Letture", "level": 0, "full_url": "https://www.promptingguide.ai/it/readings"}, {"href": "/it/course", "text": "Course", "level": 0, "full_url": "https://www.promptingguide.ai/it/course"}, {"href": "/it/agents", "text": "agents", "level": 0, "full_url": "https://www.promptingguide.ai/it/agents"}, {"href": "/it/agents/introduction", "text": "Introduction to Agents", "level": 1, "full_url": "https://www.promptingguide.ai/it/agents/introduction"}, {"href": "/it/agents/components", "text": "Agent Components", "level": 1, "full_url": "https://www.promptingguide.ai/it/agents/components"}, {"href": "/it/courses", "text": "courses", "level": 0, "full_url": "https://www.promptingguide.ai/it/courses"}, {"href": "/it/guides/optimizing-prompts", "text": "Optimizing Prompts", "level": 1, "full_url": "https://www.promptingguide.ai/it/guides/optimizing-prompts"}, {"href": "/it/guides/deep-research", "text": "OpenAI Deep Research", "level": 1, "full_url": "https://www.promptingguide.ai/it/guides/deep-research"}, {"href": "/it/guides/reasoning-llms", "text": "Reasoning LLMs", "level": 1, "full_url": "https://www.promptingguide.ai/it/guides/reasoning-llms"}]