#!/usr/bin/env python3
"""
Generatore PDF completo e ottimizzato per il sito promptingguide.ai
Versione che include tutte le pagine con pulizia avanzata.
"""

import asyncio
import json
import re
import time
from pathlib import Path
from typing import Dict, List

from bs4 import BeautifulSoup, NavigableString
from playwright.async_api import async_playwright
from weasyprint import HTML


class CompletePDFGenerator:
    def __init__(self, base_url: str = "https://www.promptingguide.ai/it"):
        self.base_url = base_url
        self.output_dir = Path("output_complete")
        self.output_dir.mkdir(exist_ok=True)
        self.pages = []
        self.navigation_structure = []

    async def extract_all_pages(self):
        """Estrae tutte le pagine dalla navigazione."""
        print("🔍 Estrazione struttura completa...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                await page.goto(self.base_url, wait_until="networkidle")
                await page.wait_for_timeout(2000)
                
                # Estrae tutti i link
                navigation_links = await page.evaluate("""
                    () => {
                        const links = [];
                        const allLinks = document.querySelectorAll('a[href^="/it"]');
                        
                        allLinks.forEach(link => {
                            const href = link.getAttribute('href');
                            const text = link.textContent.trim();
                            
                            if (href && text && 
                                !text.includes('→') && 
                                !text.includes('(opens in a new tab)') &&
                                !text.includes('Edit this page') &&
                                !text.includes('Question? Give us feedback') &&
                                text.length > 1) {
                                
                                links.push({
                                    href: href,
                                    text: text,
                                    full_url: window.location.origin + href
                                });
                            }
                        });
                        
                        return links;
                    }
                """)
                
                # Rimuove duplicati
                seen = set()
                unique_links = []
                for link in navigation_links:
                    if link['href'] not in seen:
                        seen.add(link['href'])
                        unique_links.append(link)
                
                self.navigation_structure = unique_links
                print(f"✅ Trovati {len(unique_links)} link unici")
                
                return unique_links
                
            except Exception as e:
                print(f"❌ Errore: {e}")
                return []
            finally:
                await browser.close()

    async def scrape_page_advanced(self, url: str, title: str) -> Dict:
        """Scraping avanzato con pulizia ottimizzata."""
        print(f"📄 {title[:50]}...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                await page.goto(url, wait_until="networkidle", timeout=20000)
                await page.wait_for_timeout(500)
                
                # Estrae e pulisce il contenuto
                content = await page.evaluate("""
                    () => {
                        // Lista completa di elementi da rimuovere
                        const selectorsToRemove = [
                            'nav', 'header', 'footer', 'aside', 'button',
                            '.nextra-breadcrumb', '.nextra-cards', '.nextra-callout',
                            '.nextra-button', '.nextra-copy-icon', '.nextra-code-block',
                            '[class*="nx-"]', '[class*="nextra-"]',
                            'script', 'style', 'svg', 'iframe',
                            '.print\\\\:nx-hidden', '.sr-only', '.nx-sr-only',
                            '[role="button"]', '[aria-hidden="true"]'
                        ];
                        
                        // Rimuove elementi
                        selectorsToRemove.forEach(selector => {
                            try {
                                document.querySelectorAll(selector).forEach(el => el.remove());
                            } catch(e) {}
                        });
                        
                        // Trova contenuto principale
                        const main = document.querySelector('main') || 
                                    document.querySelector('article') ||
                                    document.querySelector('.content') ||
                                    document.querySelector('[role="main"]');
                        
                        if (main) {
                            // Pulizia avanzata degli attributi
                            main.querySelectorAll('*').forEach(el => {
                                // Rimuove attributi problematici
                                const attrsToRemove = ['class', 'style', 'data-language', 
                                                     'data-theme', 'data-copy', 'data-line',
                                                     'viewbox', 'stroke-width', 'stroke-linecap',
                                                     'stroke-linejoin', 'fill', 'stroke'];
                                
                                attrsToRemove.forEach(attr => {
                                    el.removeAttribute(attr);
                                });
                                
                                // Rimuove elementi vuoti
                                if (el.tagName !== 'BR' && !el.textContent.trim() && !el.querySelector('img')) {
                                    el.remove();
                                }
                            });
                            
                            return {
                                html: main.innerHTML,
                                text: main.textContent.trim()
                            };
                        }
                        
                        return { html: '', text: '' };
                    }
                """)
                
                return {
                    'title': title,
                    'url': url,
                    'html': content['html'],
                    'text': content['text'],
                    'success': True
                }
                
            except Exception as e:
                print(f"❌ Errore in {url}: {e}")
                return {'title': title, 'url': url, 'html': '', 'text': '', 'success': False}
            finally:
                await browser.close()

    def ultra_clean_html(self, html: str) -> str:
        """Pulizia ultra-avanzata dell'HTML."""
        if not html:
            return ""
            
        soup = BeautifulSoup(html, 'html.parser')
        
        # Rimuove elementi problematici
        for element in soup(['script', 'style', 'svg', 'button', 'iframe']):
            element.decompose()
        
        # Rimuove elementi con testo specifico
        for element in soup.find_all(string=re.compile(r'(Copy code|Edit this page|Question\?|→)')):
            if element.parent:
                element.parent.decompose()
        
        # Pulisce link e immagini
        for link in soup.find_all('a', href=True):
            if link['href'].startswith('/'):
                link['href'] = f"{self.base_url}{link['href']}"
        
        for img in soup.find_all('img', src=True):
            if img['src'].startswith('/'):
                img['src'] = f"{self.base_url}{img['src']}"
        
        # Verifica se ha contenuto significativo
        text = soup.get_text().strip()
        if len(text) < 100:  # Troppo corto
            return ""
        
        return str(soup)

    def generate_complete_html(self) -> str:
        """Genera HTML completo per PDF."""
        html = f"""<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <title>Guida Completa al Prompt Engineering</title>
    <style>
        @page {{
            size: A4;
            margin: 2cm 1.5cm;
            @bottom-center {{
                content: "Pagina " counter(page) " di " counter(pages);
                font-size: 9pt;
                color: #666;
            }}
            @top-center {{
                content: "Guida al Prompt Engineering";
                font-size: 9pt;
                color: #666;
                border-bottom: 1px solid #ddd;
                padding-bottom: 5pt;
            }}
        }}
        
        @page:first {{
            @top-center {{ content: none; }}
            @bottom-center {{ content: none; }}
        }}
        
        body {{
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            font-size: 10pt;
            margin: 0;
            padding: 0;
        }}
        
        .title-page {{
            text-align: center;
            padding: 6cm 0;
            page-break-after: always;
        }}
        
        .title-page h1 {{
            font-size: 3em;
            color: #2c3e50;
            margin-bottom: 0.5em;
            font-weight: 300;
        }}
        
        .title-page .subtitle {{
            font-size: 1.3em;
            color: #7f8c8d;
            margin-bottom: 2em;
        }}
        
        .title-page .info {{
            font-size: 1em;
            color: #95a5a6;
            line-height: 1.8;
        }}
        
        .toc {{
            page-break-after: always;
            margin-bottom: 2em;
        }}
        
        .toc h2 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5em;
            margin-bottom: 1.5em;
            font-size: 1.8em;
        }}
        
        .toc ul {{
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            column-gap: 2em;
        }}
        
        .toc li {{
            margin: 0.4em 0;
            padding: 0.2em 0;
            break-inside: avoid;
        }}
        
        .chapter {{
            page-break-before: always;
            margin-bottom: 2em;
        }}
        
        .chapter-title {{
            color: #2c3e50;
            font-size: 1.6em;
            font-weight: 600;
            margin: 0 0 1.5em 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5em;
            page-break-after: avoid;
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
            page-break-after: avoid;
            font-weight: 600;
        }}
        
        h1 {{ font-size: 1.4em; }}
        h2 {{ font-size: 1.2em; }}
        h3 {{ font-size: 1.1em; }}
        h4 {{ font-size: 1.05em; }}
        
        p {{
            margin: 0.8em 0;
            text-align: justify;
            orphans: 2;
            widows: 2;
        }}
        
        pre, code {{
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.85em;
        }}
        
        pre {{
            padding: 0.8em;
            margin: 1em 0;
            page-break-inside: avoid;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }}
        
        code {{
            padding: 0.2em 0.4em;
            background-color: #f1f3f4;
        }}
        
        blockquote {{
            border-left: 4px solid #3498db;
            margin: 1em 0;
            padding: 0.5em 1em;
            background-color: #f8f9fa;
            font-style: italic;
            page-break-inside: avoid;
        }}
        
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
            font-size: 0.9em;
        }}
        
        th, td {{
            border: 1px solid #ddd;
            padding: 0.4em;
            text-align: left;
            vertical-align: top;
        }}
        
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
            color: #2c3e50;
        }}
        
        ul, ol {{
            margin: 0.8em 0;
            padding-left: 1.8em;
        }}
        
        li {{
            margin: 0.3em 0;
        }}
        
        strong, b {{
            font-weight: 600;
            color: #2c3e50;
        }}
        
        a {{
            color: #3498db;
            text-decoration: none;
        }}
        
        img {{
            max-width: 100%;
            height: auto;
            page-break-inside: avoid;
            margin: 0.5em 0;
        }}
    </style>
</head>
<body>
    <div class="title-page">
        <h1>Guida Completa al Prompt Engineering</h1>
        <div class="subtitle">Versione PDF Completa</div>
        <div class="info">
            Fonte: promptingguide.ai<br>
            Generato: {time.strftime("%d/%m/%Y alle %H:%M")}<br>
            Totale pagine: {len(self.pages)}<br>
            Versione italiana
        </div>
    </div>
    
    <div class="toc">
        <h2>Indice dei Contenuti</h2>
        <ul>
"""
        
        # Indice
        for i, page in enumerate(self.pages, 1):
            if page['success'] and page['html'].strip():
                clean_title = re.sub(r'[^\w\s\-àèéìíîòóùú]', '', page['title']).strip()
                if clean_title:
                    html += f"            <li>{i}. {clean_title}</li>\n"
        
        html += """        </ul>
    </div>
"""
        
        # Contenuto
        for i, page in enumerate(self.pages, 1):
            if page['success'] and page['html'].strip():
                clean_title = re.sub(r'[^\w\s\-àèéìíîòóùú]', '', page['title']).strip()
                if clean_title:
                    html += f"""
    <div class="chapter">
        <div class="chapter-title">{i}. {clean_title}</div>
        <div class="content">{page['html']}</div>
    </div>
"""
        
        html += """
</body>
</html>"""
        
        return html

    async def generate_complete_pdf(self):
        """Processo completo di generazione PDF."""
        print("🚀 Avvio generazione PDF completo...")
        start_time = time.time()
        
        # 1. Estrai tutte le pagine
        await self.extract_all_pages()
        
        # 2. Scraping con batch processing
        print("📥 Scraping di tutte le pagine...")
        batch_size = 10
        
        for i in range(0, len(self.navigation_structure), batch_size):
            batch = self.navigation_structure[i:i+batch_size]
            print(f"📦 Batch {i//batch_size + 1}/{(len(self.navigation_structure)-1)//batch_size + 1}")
            
            for link in batch:
                result = await self.scrape_page_advanced(link['full_url'], link['text'])
                if result['success']:
                    result['html'] = self.ultra_clean_html(result['html'])
                    if result['html']:
                        self.pages.append(result)
                
                await asyncio.sleep(0.3)  # Pausa tra richieste
        
        print(f"✅ Raccolte {len(self.pages)} pagine con contenuto valido")
        
        # 3. Genera HTML
        print("📝 Generazione HTML completo...")
        html_content = self.generate_complete_html()
        
        # Salva HTML
        html_file = self.output_dir / "complete_guide.html"
        with open(html_file, "w", encoding="utf-8") as f:
            f.write(html_content)
        
        # 4. Genera PDF
        print("📄 Generazione PDF finale...")
        pdf_file = self.output_dir / "prompt_engineering_guide_complete.pdf"
        
        try:
            HTML(string=html_content, base_url=self.base_url).write_pdf(
                pdf_file,
                optimize_images=True,
                presentational_hints=True
            )
            
            elapsed_time = time.time() - start_time
            print(f"🎉 PDF completo generato in {elapsed_time:.2f} secondi!")
            print(f"📄 File: {pdf_file}")
            print(f"📊 Dimensione: {pdf_file.stat().st_size / 1024 / 1024:.2f} MB")
            print(f"📚 Pagine processate: {len(self.pages)}")
            
        except Exception as e:
            print(f"❌ Errore nella generazione PDF: {e}")


async def main():
    generator = CompletePDFGenerator()
    await generator.generate_complete_pdf()


if __name__ == "__main__":
    asyncio.run(main())
