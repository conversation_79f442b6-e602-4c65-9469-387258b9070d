<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <title>Guida Completa al Prompt Engineering</title>
    <style>
        @page {
            size: A4;
            margin: 2cm 1.5cm;
            @bottom-center {
                content: "Pagina " counter(page) " di " counter(pages);
                font-size: 9pt;
                color: #666;
            }
            @top-center {
                content: "Guida al Prompt Engineering";
                font-size: 9pt;
                color: #666;
                border-bottom: 1px solid #ddd;
                padding-bottom: 5pt;
            }
        }
        
        @page:first {
            @top-center { content: none; }
            @bottom-center { content: none; }
        }
        
        body {
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            font-size: 10pt;
            margin: 0;
            padding: 0;
        }
        
        .title-page {
            text-align: center;
            padding: 6cm 0;
            page-break-after: always;
        }
        
        .title-page h1 {
            font-size: 3em;
            color: #2c3e50;
            margin-bottom: 0.5em;
            font-weight: 300;
        }
        
        .title-page .subtitle {
            font-size: 1.3em;
            color: #7f8c8d;
            margin-bottom: 2em;
        }
        
        .title-page .info {
            font-size: 1em;
            color: #95a5a6;
            line-height: 1.8;
        }
        
        .toc {
            page-break-after: always;
            margin-bottom: 2em;
        }
        
        .toc h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5em;
            margin-bottom: 1.5em;
            font-size: 1.8em;
        }
        
        .toc ul {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            column-gap: 2em;
        }
        
        .toc li {
            margin: 0.4em 0;
            padding: 0.2em 0;
            break-inside: avoid;
        }
        
        .chapter {
            page-break-before: always;
            margin-bottom: 2em;
        }
        
        .chapter-title {
            color: #2c3e50;
            font-size: 1.6em;
            font-weight: 600;
            margin: 0 0 1.5em 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5em;
            page-break-after: avoid;
        }
        
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
            page-break-after: avoid;
            font-weight: 600;
        }
        
        h1 { font-size: 1.4em; }
        h2 { font-size: 1.2em; }
        h3 { font-size: 1.1em; }
        h4 { font-size: 1.05em; }
        
        p {
            margin: 0.8em 0;
            text-align: justify;
            orphans: 2;
            widows: 2;
        }
        
        pre, code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.85em;
        }
        
        pre {
            padding: 0.8em;
            margin: 1em 0;
            page-break-inside: avoid;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        code {
            padding: 0.2em 0.4em;
            background-color: #f1f3f4;
        }
        
        blockquote {
            border-left: 4px solid #3498db;
            margin: 1em 0;
            padding: 0.5em 1em;
            background-color: #f8f9fa;
            font-style: italic;
            page-break-inside: avoid;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
            font-size: 0.9em;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 0.4em;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            color: #2c3e50;
        }
        
        ul, ol {
            margin: 0.8em 0;
            padding-left: 1.8em;
        }
        
        li {
            margin: 0.3em 0;
        }
        
        strong, b {
            font-weight: 600;
            color: #2c3e50;
        }
        
        a {
            color: #3498db;
            text-decoration: none;
        }
        
        img {
            max-width: 100%;
            height: auto;
            page-break-inside: avoid;
            margin: 0.5em 0;
        }
    </style>
</head>
<body>
    <div class="title-page">
        <h1>Guida Completa al Prompt Engineering</h1>
        <div class="subtitle">Versione PDF Completa</div>
        <div class="info">
            Fonte: promptingguide.ai<br>
            Generato: 23/06/2025 alle 13:00<br>
            Totale pagine: 0<br>
            Versione italiana
        </div>
    </div>
    
    <div class="toc">
        <h2>Indice dei Contenuti</h2>
        <ul>
        </ul>
    </div>

</body>
</html>