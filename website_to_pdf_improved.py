#!/usr/bin/env python3
"""
Script migliorato per convertire il sito web https://www.promptingguide.ai/it in un PDF pulito.
Versione ottimizzata con migliore pulizia HTML e formattazione PDF.
"""

import asyncio
import json
import os
import re
import time
from pathlib import Path
from typing import Dict, List, Tuple
from urllib.parse import urljoin, urlparse

import requests
from bs4 import BeautifulSoup, NavigableString
from playwright.async_api import async_playwright
from weasyprint import HTML, CSS


class ImprovedPromptGuideToPDF:
    def __init__(self, base_url: str = "https://www.promptingguide.ai/it"):
        self.base_url = base_url
        self.output_dir = Path("output_improved")
        self.output_dir.mkdir(exist_ok=True)
        
        # Struttura per memorizzare le pagine
        self.pages = []
        self.navigation_structure = {}
        
        # Headers per le richieste
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

    async def extract_navigation_structure(self):
        """Estrae la struttura di navigazione dal sito web."""
        print("🔍 Estrazione struttura di navigazione...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                await page.goto(self.base_url, wait_until="networkidle")
                await page.wait_for_timeout(2000)
                
                # Estrae tutti i link che iniziano con /it
                navigation_links = await page.evaluate("""
                    () => {
                        const links = [];
                        const allLinks = document.querySelectorAll('a[href^="/it"]');
                        
                        allLinks.forEach(link => {
                            const href = link.getAttribute('href');
                            const text = link.textContent.trim();
                            
                            // Filtra link validi e rimuove duplicati
                            if (href && text && 
                                !text.includes('→') && 
                                !text.includes('(opens in a new tab)') &&
                                !text.includes('Edit this page') &&
                                !text.includes('Question? Give us feedback') &&
                                text.length > 1) {
                                
                                // Determina il livello dalla struttura DOM
                                let level = 0;
                                let parent = link.parentElement;
                                
                                while (parent && level < 5) {
                                    if (parent.tagName === 'UL' || parent.tagName === 'OL') {
                                        level++;
                                    }
                                    parent = parent.parentElement;
                                }
                                
                                links.push({
                                    href: href,
                                    text: text,
                                    level: Math.max(0, level - 1),
                                    full_url: window.location.origin + href
                                });
                            }
                        });
                        
                        return links;
                    }
                """)
                
                # Rimuove duplicati mantenendo l'ordine
                seen = set()
                unique_links = []
                for link in navigation_links:
                    if link['href'] not in seen:
                        seen.add(link['href'])
                        unique_links.append(link)
                
                self.navigation_structure = unique_links
                print(f"✅ Trovati {len(unique_links)} link unici nella navigazione")
                
                # Salva la struttura per debug
                with open(self.output_dir / "navigation_structure.json", "w", encoding="utf-8") as f:
                    json.dump(unique_links, f, indent=2, ensure_ascii=False)
                
                return unique_links
                
            except Exception as e:
                print(f"❌ Errore nell'estrazione della navigazione: {e}")
                return []
            finally:
                await browser.close()

    async def scrape_page_content(self, url: str, title: str) -> Dict:
        """Scraping del contenuto di una singola pagina con pulizia migliorata."""
        print(f"📄 Scraping: {title} ({url})")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                await page.goto(url, wait_until="networkidle")
                await page.wait_for_timeout(1000)
                
                # Estrae il contenuto principale con pulizia avanzata
                content = await page.evaluate("""
                    () => {
                        // Rimuove elementi non necessari
                        const elementsToRemove = [
                            'nav', 'header', 'footer', '.navigation', '.sidebar',
                            '.breadcrumb', '.toc', '.table-of-contents',
                            'script', 'style', '.ad', '.advertisement',
                            '.nextra-breadcrumb', '.nextra-cards', '.nextra-callout',
                            '[class*="nx-"]', '.print\\:nx-hidden',
                            '.nextra-button', '.nextra-copy-icon',
                            'button', '.sr-only', '.nx-sr-only'
                        ];
                        
                        elementsToRemove.forEach(selector => {
                            try {
                                const elements = document.querySelectorAll(selector);
                                elements.forEach(el => el.remove());
                            } catch(e) {
                                // Ignora errori di selettore
                            }
                        });
                        
                        // Cerca il contenuto principale
                        const mainContent = document.querySelector('main') ||
                                          document.querySelector('[role="main"]') ||
                                          document.querySelector('.content') ||
                                          document.querySelector('.main-content') ||
                                          document.querySelector('article');
                        
                        if (mainContent) {
                            // Rimuove attributi di stile inline e classi CSS
                            const allElements = mainContent.querySelectorAll('*');
                            allElements.forEach(el => {
                                // Rimuove classi CSS specifiche di Nextra
                                if (el.className) {
                                    el.className = el.className
                                        .split(' ')
                                        .filter(cls => !cls.startsWith('nx-') && 
                                                      !cls.startsWith('nextra-') &&
                                                      !cls.includes('dark:') &&
                                                      !cls.includes('hover:') &&
                                                      !cls.includes('contrast-'))
                                        .join(' ');
                                }
                                
                                // Rimuove attributi di stile inline
                                el.removeAttribute('style');
                                
                                // Rimuove attributi specifici
                                ['data-language', 'data-theme', 'viewbox', 'stroke-width',
                                 'stroke-linecap', 'stroke-linejoin', 'fill', 'stroke'].forEach(attr => {
                                    el.removeAttribute(attr);
                                });
                            });
                            
                            return {
                                html: mainContent.innerHTML,
                                text: mainContent.textContent.trim()
                            };
                        }
                        
                        return {
                            html: '',
                            text: ''
                        };
                    }
                """)
                
                return {
                    'url': url,
                    'title': title,
                    'html': content['html'],
                    'text': content['text'],
                    'success': True
                }
                
            except Exception as e:
                print(f"❌ Errore nel scraping di {url}: {e}")
                return {
                    'url': url,
                    'title': title,
                    'html': '',
                    'text': '',
                    'success': False,
                    'error': str(e)
                }
            finally:
                await browser.close()

    def deep_clean_html(self, html: str) -> str:
        """Pulizia profonda dell'HTML per rimuovere elementi problematici."""
        if not html:
            return ""
            
        soup = BeautifulSoup(html, 'html.parser')
        
        # Rimuove script, style e commenti
        for element in soup(["script", "style"]):
            element.decompose()
        
        # Rimuove commenti HTML
        for comment in soup.find_all(string=lambda text: isinstance(text, NavigableString) and text.strip().startswith('<!--')):
            comment.extract()
        
        # Rimuove elementi con classi problematiche
        problematic_classes = [
            'nextra-breadcrumb', 'nextra-cards', 'nextra-callout', 'nextra-code-block',
            'nextra-button', 'nextra-copy-icon', 'print:nx-hidden'
        ]
        
        for class_name in problematic_classes:
            for element in soup.find_all(class_=lambda x: x and class_name in x):
                element.decompose()
        
        # Pulisce tutti gli elementi
        for element in soup.find_all():
            # Rimuove attributi problematici
            attrs_to_remove = []
            for attr in element.attrs:
                if (attr.startswith('data-') or 
                    attr in ['style', 'class'] or
                    'nx-' in str(element.attrs[attr]) or
                    'nextra-' in str(element.attrs[attr])):
                    attrs_to_remove.append(attr)
            
            for attr in attrs_to_remove:
                del element.attrs[attr]
        
        # Converte link e immagini relative in assolute
        for link in soup.find_all('a', href=True):
            if link['href'].startswith('/'):
                link['href'] = urljoin(self.base_url, link['href'])
        
        for img in soup.find_all('img', src=True):
            if img['src'].startswith('/'):
                img['src'] = urljoin(self.base_url, img['src'])
        
        # Rimuove SVG complessi (icone)
        for svg in soup.find_all('svg'):
            svg.decompose()
        
        return str(soup)

    async def scrape_all_pages(self):
        """Scraping di tutte le pagine della navigazione."""
        if not self.navigation_structure:
            await self.extract_navigation_structure()
        
        print(f"🚀 Inizio scraping di {len(self.navigation_structure)} pagine...")
        
        for i, link in enumerate(self.navigation_structure, 1):
            print(f"[{i}/{len(self.navigation_structure)}] ", end="")
            
            page_data = await self.scrape_page_content(link['full_url'], link['text'])
            page_data['level'] = link['level']
            page_data['order'] = i
            
            if page_data['success']:
                page_data['html'] = self.deep_clean_html(page_data['html'])
                self.pages.append(page_data)
            
            # Pausa tra le richieste
            await asyncio.sleep(0.5)
        
        print(f"✅ Scraping completato! {len(self.pages)} pagine raccolte con successo.")

    def generate_clean_pdf_html(self) -> str:
        """Genera HTML pulito e ottimizzato per il PDF."""
        print("📝 Generazione HTML pulito per PDF...")

        html_parts = [
            """<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guida al Prompt Engineering</title>
    <style>
        @page {
            size: A4;
            margin: 2.5cm 2cm;
            @bottom-center {
                content: "Pagina " counter(page);
                font-size: 10pt;
                color: #666;
            }
            @top-center {
                content: "Guida al Prompt Engineering";
                font-size: 10pt;
                color: #666;
                border-bottom: 1px solid #ddd;
                padding-bottom: 5pt;
            }
        }

        @page:first {
            @top-center { content: none; }
            @bottom-center { content: none; }
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            font-size: 11pt;
            margin: 0;
            padding: 0;
        }

        .title-page {
            text-align: center;
            padding: 5cm 0;
            page-break-after: always;
        }

        .title-page h1 {
            font-size: 3em;
            color: #2c3e50;
            margin-bottom: 1em;
            font-weight: 300;
        }

        .title-page .subtitle {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 2em;
        }

        .title-page .info {
            font-size: 1em;
            color: #95a5a6;
        }

        .toc {
            page-break-after: always;
            margin-bottom: 2em;
        }

        .toc h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5em;
            margin-bottom: 1em;
        }

        .toc ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .toc li {
            margin: 0.3em 0;
            padding: 0.2em 0;
        }

        .toc .level-0 {
            font-weight: bold;
            font-size: 1.1em;
            color: #2c3e50;
            margin-top: 1em;
        }
        .toc .level-1 {
            padding-left: 1.5em;
            color: #34495e;
        }
        .toc .level-2 {
            padding-left: 3em;
            color: #7f8c8d;
            font-size: 0.95em;
        }

        .page-break {
            page-break-before: always;
        }

        .chapter {
            margin-bottom: 2em;
        }

        .chapter-title {
            color: #2c3e50;
            font-size: 1.8em;
            font-weight: 600;
            margin: 2em 0 1em 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5em;
            page-break-after: avoid;
        }

        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
            page-break-after: avoid;
            font-weight: 600;
        }

        h1 { font-size: 1.6em; }
        h2 { font-size: 1.4em; }
        h3 { font-size: 1.2em; }
        h4 { font-size: 1.1em; }
        h5, h6 { font-size: 1em; }

        p {
            margin: 0.8em 0;
            text-align: justify;
            orphans: 2;
            widows: 2;
        }

        pre, code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
        }

        pre {
            padding: 1em;
            margin: 1em 0;
            overflow-x: auto;
            page-break-inside: avoid;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        code {
            padding: 0.2em 0.4em;
            background-color: #f1f3f4;
        }

        blockquote {
            border-left: 4px solid #3498db;
            margin: 1em 0;
            padding: 0.5em 1em;
            background-color: #f8f9fa;
            font-style: italic;
            page-break-inside: avoid;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
            font-size: 0.95em;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 0.5em;
            text-align: left;
            vertical-align: top;
        }

        th {
            background-color: #f2f2f2;
            font-weight: bold;
            color: #2c3e50;
        }

        img {
            max-width: 100%;
            height: auto;
            page-break-inside: avoid;
            margin: 1em 0;
        }

        ul, ol {
            margin: 0.8em 0;
            padding-left: 2em;
        }

        li {
            margin: 0.3em 0;
        }

        a {
            color: #3498db;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        strong, b {
            font-weight: 600;
            color: #2c3e50;
        }

        em, i {
            font-style: italic;
        }

        .content {
            max-width: none;
        }

        /* Nasconde elementi non necessari per la stampa */
        .no-print {
            display: none;
        }
    </style>
</head>
<body>
"""
        ]

        # Pagina del titolo
        html_parts.append("""
    <div class="title-page">
        <h1>Guida al Prompt Engineering</h1>
        <div class="subtitle">Versione completa in PDF</div>
        <div class="info">
            Fonte: promptingguide.ai<br>
            Generato il: """ + time.strftime("%d/%m/%Y") + """<br>
            Pagine totali: """ + str(len(self.pages)) + """
        </div>
    </div>
""")

        # Indice
        html_parts.append('<div class="toc"><h2>Indice</h2><ul>')
        for page in self.pages:
            level_class = f"level-{min(page['level'], 2)}"
            # Pulisce il titolo per l'indice
            clean_title = re.sub(r'[^\w\s\-àèéìíîòóùú]', '', page['title']).strip()
            if clean_title:
                html_parts.append(f'<li class="{level_class}">{clean_title}</li>')
        html_parts.append('</ul></div>')

        # Contenuto delle pagine
        for page in self.pages:
            if page['html'].strip():
                clean_title = re.sub(r'[^\w\s\-àèéìíîòóùú]', '', page['title']).strip()
                if clean_title:
                    html_parts.append(f'<div class="page-break"></div>')
                    html_parts.append(f'<div class="chapter">')
                    html_parts.append(f'<div class="chapter-title">{clean_title}</div>')
                    html_parts.append(f'<div class="content">{page["html"]}</div>')
                    html_parts.append(f'</div>')

        html_parts.append('</body></html>')

        return '\n'.join(html_parts)

    def generate_improved_pdf(self):
        """Genera il PDF migliorato."""
        print("📄 Generazione PDF migliorato...")

        html_content = self.generate_clean_pdf_html()

        # Salva l'HTML per debug
        html_file = self.output_dir / "clean_guide.html"
        with open(html_file, "w", encoding="utf-8") as f:
            f.write(html_content)

        # Genera il PDF
        pdf_file = self.output_dir / "prompt_engineering_guide_clean.pdf"

        try:
            HTML(string=html_content, base_url=self.base_url).write_pdf(
                pdf_file,
                stylesheets=[],
                optimize_images=True,
                presentational_hints=True
            )
            print(f"✅ PDF migliorato generato con successo: {pdf_file}")
            print(f"📊 Dimensione file: {pdf_file.stat().st_size / 1024 / 1024:.2f} MB")

        except Exception as e:
            print(f"❌ Errore nella generazione PDF: {e}")

    async def run(self):
        """Esegue l'intero processo di conversione migliorato."""
        print("🚀 Avvio conversione migliorata sito web in PDF...")
        start_time = time.time()

        try:
            # Step 1: Estrai struttura navigazione
            await self.extract_navigation_structure()

            # Step 2: Scraping di tutte le pagine
            await self.scrape_all_pages()

            # Step 3: Genera PDF migliorato
            self.generate_improved_pdf()

            elapsed_time = time.time() - start_time
            print(f"🎉 Conversione migliorata completata in {elapsed_time:.2f} secondi!")

        except Exception as e:
            print(f"❌ Errore durante la conversione: {e}")


async def main():
    converter = ImprovedPromptGuideToPDF()
    await converter.run()


if __name__ == "__main__":
    asyncio.run(main())
